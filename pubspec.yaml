name: managementdoc
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Firebase
  firebase_core: ^3.8.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  firebase_storage: ^12.3.6
  firebase_app_check: ^0.3.2+7

  # State Management
  provider: ^6.1.1

  # Local Storage
  shared_preferences:
    ^2.2.2

    # Gunakan file_selector (plugin resmi Flutter)
  file_selector: ^1.0.1

  # Pastikan semua platform didukung
  file_selector_linux: ^0.9.2
  file_selector_macos: ^0.9.3
  file_selector_windows: ^0.9.3
  file_selector_web: ^0.9.1
  file_selector_ios: ^0.5.3+1
  file_selector_android: ^0.5.0
  path_provider: ^2.1.1
  image_picker: ^1.0.4

  # UI & Utils
  cached_network_image: ^3.3.0
  intl: ^0.19.0
  flutter_spinkit: ^5.2.0
  fluttertoast: ^8.2.4
  permission_handler: ^11.1.0
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.1.0
  shimmer: ^3.0.0
  uuid: ^4.2.1
  path: ^1.8.3

  # File Download
  dio: ^5.4.0
  external_path: ^2.2.0

  # Native Notifications
  flutter_local_notifications: ^17.2.3

  # HTTP Client for Cloud Functions
  http: ^1.1.0

  # Cloud Functions
  cloud_functions: ^5.1.3

  # Cryptography for file hashing
  crypto: ^3.0.3

  # Google Drive integration
  googleapis: ^13.2.0
  google_sign_in: ^6.2.1

  # Native sharing functionality (simplified)
  share_plus: ^10.1.2

  # File Preview Dependencies
  flutter_pdfview: ^1.3.2
  webview_flutter: ^4.4.2
  photo_view: ^0.14.0
  video_player: ^2.8.1
  audioplayers: ^5.2.1
  url_launcher: ^6.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Firebase Testing Mocks
  fake_cloud_firestore: ^3.0.3
  firebase_storage_mocks: ^0.7.0
  firebase_auth_mocks: ^0.14.1
  mockito: ^5.4.4
  build_runner: ^2.4.13

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/icon/home.svg
    - assets/icon/folder.svg
    - assets/icon/plus.svg
    - assets/icon/user.svg
    - assets/icon/add-user.svg
    - assets/icon/Activity.svg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/app_icon.png"
    background_color: "#1976d2"
    theme_color: "#1976d2"
  windows:
    generate: true
    image_path: "assets/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/app_icon.png"
